

# Tipli

## Základní doporučení pro vývoj aplikace


### pull requesty
- každ<PERSON> pull request musí mít přiděleno skrz `Assignee` <PERSON>, který je zodpovědný za uzavření daného pull requestu
- pokud je pull request rozpracován, je možné ho vytvořit ve stavu `draft` anebo k němu přidělit tag `no merge`
- v případě, že je pull request považován jako hotový, je nutné přivolat review označením člověka zodpovědného za review a nasazení (aktuálně `@jirikrenek`)
- nové větve vytváříme vždy z čerstvě pullnutého masteru
- názvy větví píšeme anglicky, co nejvystižněji a používáme `kebab case`

### překlady
výchozí slovník je český (`cs_CZ`). V případě přidání nových překladů, je nutné fráze zkopírovat do všech ostatních slovníků s `@todo` nakonci každé fráze. Např.:

``klicoveSlovo: 'Přihlásit se @todo'``

Aplikace pak sama nabídne v [administračním rozhraní](http://tipli.cz/admin/translations.translation/) všechny pull requesty obsahující `@todo` ve slovnících a nabídne rozhraní pro překladatele, kteří můžou do daného pull requestu doplnit překlad.

### Architektura aplikace
Aplikace se skládá z objektů: Presenter, Control, Facade, Manager, Repository, Command.
- Presenter - zpracovává signály a předává dál do modelu
- Control - formulář, vytvářen vždy interface továrničkou
- Facade - příjímá signály z venku a předává dál do Manager nebo Repository
- Manager - vytváří a persistuje konkrétní entity
- Repository - načítá z databáze entity
- Command - spouští příkazy, které jsou ve většině případů spouštěny automaticky cronem

![architecture](/www/images/architecture-example.png)

## Instalace aplikace
### virtualhosts
Aplikaci spouštíme na těchto doménách:

- `tipli.czlocal` (CZ lokalizace)
- `tipli.sklocal` (SK lokalizace)
- `tipli.pllocal` (PL lokalizace)
- `tipli.rolocal` (RO lokalizace)

Všechny tyto domény je třeba nasměrovat do složky `/www`. Aplikace sama pozná, přes kterou doménu je přistupováno a nastaví podle ní danou lokalizaci. Všechny domény musí mít také vygenerovaný SSL certifikát (aplikace sama vynucuje https).

### config
Je nutné nakopírovat `config.local.example.neon` jako `config.local.neon`. V configu je pak třeba nastavit přistupové údaje do databáze (sekce `db`).

### databáze
Databáze by měla mít porovnání `utf8_unicode_ci`. Jako název databáze doporučujeme používat `tipli`.
Po vytvoření databáze je třeba vytvořit schéma příkazem:

``php bin/console orm:schema-tool:update --force``

Po vytvoření schéma je třeba naimportovat soubor `data/data.sql`.

## Docker
- ``bin/helper start`` nastartuje kontejner včetně instalace vendoru
- ``bin/helper start --soft`` nastartuje kontejner bez instalace vendoru
- ``bin/helper restart --soft`` restartuje kontejner bez instalace vendoru
- ``bin/helper stop`` zastaví kontejner
- ``bin/helper in`` spustí shell v kontejneru
### nastavení nginx reverse proxy:
```
server {
	listen 80;
	server_name tipli.czlocal;
        location / {
            proxy_pass http://127.0.0.1:8085;
        }
    }
```
## Coding standard
Používáme coding standard, který kontrolujeme přes [PHP_CodeSniffer](https://github.com/squizlabs/PHP_CodeSniffer). Sniffy používáme od [slevomat/coding-standard](https://github.com/slevomat/coding-standard)

### kontrola kódu
``vendor/bin/phpcs --standard=ruleset.xml --extensions=php --tab-width=4 -sp app``

### automatické fixy kódu
``vendor/bin/phpcbf --standard=ruleset.xml --extensions=php --tab-width=4 -sp app``

## PHPStan
### kontrola kódu
``vendor/bin/phpstan analyse app -l 4 --memory-limit=4G -c app/Config/phpstan.neon --autoload-file=tests/phpstan/autoload-phpstan.php``
...
...
.....
.....

php -d memory_limit=2G vendor/bin/codecept run -c tests/codeception/codeception.yml --env czech
php -d memory_limit=2G vendor/bin/codecept run -c tests/codeception/codeception.yml --env czech tests/codeception/tests/acceptance/LuckyShopsCest.php
php -d memory_limit=2G vendor/bin/codecept run acceptance -c tests/codeception/codeception.yml --env czech tests/codeception/tests/acceptance/LuckyShopsCest.php
